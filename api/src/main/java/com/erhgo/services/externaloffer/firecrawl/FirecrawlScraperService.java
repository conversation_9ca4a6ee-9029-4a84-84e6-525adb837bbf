package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.openapi.dto.ScrapeOffersCommandDTO;
import com.erhgo.repositories.ExternalOfferRepository;
import com.erhgo.services.externaloffer.config.AtsGetOfferConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.List;
import java.util.stream.StreamSupport;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnExpression("!T(org.springframework.util.StringUtils).isEmpty('${firecrawl.api-key:}')")
public class FirecrawlScraperService implements ExternalOfferScraper {

    private static final String FIRECRAWL_ATS_CODE = "FIRECRAWL";

    private static final String EXTRACTION_PROMPT = """
            Extraire uniquement la section de description complète du poste en HTML.
            Conserver toutes les balises HTML d'origine (div, span, ul, li, etc.) et les attributs de style.
            Important:
            - Inclure uniquement le contenu qui décrit le poste, les responsabilités, les qualifications requises,
              les avantages, et autres informations relatives au poste.
            - Ne pas inclure les formulaires de candidature, les boutons pour postuler, les menus de navigation,
              les en-têtes du site, les pieds de page, ou tout autre élément qui n'est pas directement lié à la
              description du poste.
            - Exclure les tableaux de navigation (comme les flèches directionnelles ou zoom).
            - Ignorer les sections de politique de confidentialité, cookies, ou d'autres éléments non pertinents.
            
            Sérialise l'offre au format JSON avec les propriétés suivantes:
            - title: Titre du poste
            - contract: Type de contrat (CDI ou CDD, ou autres types)
            - location: Lieu de travail
            - salary: Salaire annuel si disponible (convertir en format "XXXXX€" si nécessaire), sinon null
            - url: URL de l'offre %s
            - job_description: Description complète du poste en HTML
            - organization_description: Description de l'entreprise ou organisation offrant le poste (extraire toute section "À propos de nous", "Qui sommes-nous", ou similaire qui présente l'entreprise)
            - publication_date: UNIQUEMENT la date de publication de l'annonce (quand l'offre a été publiée sur le site) au format dd-MM-yyyy'T'HH:mm, sinon null. 
            
            INTERDICTION ABSOLUE: Ne JAMAIS utiliser les dates suivantes pour publication_date:
            * "Poste à pourvoir à partir du" ou "à pourvoir dès le" + date
            * "Prise de poste le" + date
            * "Date de début souhaitée" + date
            * "Début de mission" + date
            * "Entrée en fonction" + date
            * "Date de démarrage" + date
            * "Disponibilité requise" + date
            Ces dates concernent le DÉBUT DU TRAVAIL, pas la publication de l'annonce.
            
            Si aucune date de publication explicite n'est trouvée (comme "Publié le", "Annonce du", "Mise en ligne le"), alors publication_date doit être null.
            
            Si le salaire est indiqué sous forme "€30k" ou "30k€", convertir en "30000€".
            
               Pour l'organization_description, chercher spécifiquement:
            - Les sections décrivant l'entreprise, sa mission, ses valeurs
            - Les sections intitulées "À propos de [nom de l'entreprise]", "L'entreprise", "Qui sommes-nous", etc.
            - Les paragraphes décrivant l'historique, la taille, le secteur d'activité de l'entreprise
            - Les sections sur la culture d'entreprise, l'environnement de travail
            - Toutes les mentions de l'entreprise dans le contexte de l'offre d'emploi
            
            Ne jamais renvoyer de valeur vide pour organization_description. Si aucune information sur l'entreprise n'est
            trouvée explicitement, analyser le contenu général de la page pour extraire toute mention contextuelle de l'entreprise. N'invente pas de contenu.
            """;

    private static final String LINKS_FORMAT = "links";
    private static final String EXTRACT_FORMAT = "extract";
    private static final String HTML_FORMAT = "html";
    private static final long TIMEOUT = 120000;
    private static final long WAIT_FOR = 10000;
    private final ObjectMapper objectMapper;
    private final ExternalOfferRepository externalOfferRepository;
    @VisibleForTesting
    @Setter
    private RestTemplate restTemplate;
    @Value("${firecrawl.api-key}")
    private String apiKey;
    @Value("${firecrawl.api-url}")
    private String apiUrl;
    @Value("${firecrawl.timeout-in-ms.connect}")
    private long connectTimeoutMs;
    @Value("${firecrawl.timeout-in-ms.read}")
    private long readTimeoutMs;
    @Autowired
    private RestTemplateBuilder restTemplateBuilder;

    @PostConstruct
    public void initializeRestTemplate() {
        log.info("Initializing Firecrawl scraper service with REST template");
        this.restTemplate = restTemplateBuilder
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .setConnectTimeout(Duration.ofMillis(connectTimeoutMs))
                .setReadTimeout(Duration.ofMillis(readTimeoutMs))
                .build();
    }

    @Override
    public List<String> scrapeJobsAtUrl(String url, String mustContain, String mustNotContain) throws InvalidScrapingException {
        var requestBody = buildBaseRequest(url);
        requestBody.putArray("formats").add(LINKS_FORMAT);

        var response = callFirecrawlApi(requestBody);
        var linksNode = response.path("data").path(LINKS_FORMAT);

        return StreamSupport.stream(linksNode.spliterator(), false)
                .map(JsonNode::asText)
                .filter(jobUrl -> mustContain == null || jobUrl.contains(mustContain))
                .filter(jobUrl -> mustNotContain == null || !jobUrl.contains(mustNotContain))
                .toList();
    }

    @Override
    public JsonNode scrapeJobDetails(String jobUrl) {
        try {
            var requestBody = buildBaseRequest(jobUrl);
            requestBody.putArray("formats").add(EXTRACT_FORMAT).add(HTML_FORMAT);
            buildPrompt(requestBody, jobUrl);
            requestBody.put("timeout", TIMEOUT);
            requestBody.put("waitFor", WAIT_FOR);
            var response = callFirecrawlApi(requestBody);
            return response.path("data").path(EXTRACT_FORMAT);
        } catch (InvalidScrapingException e) {
            log.warn("Unable to retrieve job details at URL {}: {}", jobUrl, e.getMessage(), e);
            return objectMapper.nullNode();
        }
    }


    private ObjectNode buildBaseRequest(String url) {
        var requestBody = objectMapper.createObjectNode();
        requestBody.put("url", url);
        return requestBody;
    }

    private void buildPrompt(ObjectNode requestBody, String url) {
        var extractNode = requestBody.putObject(EXTRACT_FORMAT);
        extractNode.put("prompt", EXTRACTION_PROMPT.formatted(url));
    }


    private JsonNode callFirecrawlApi(ObjectNode requestBody) throws InvalidScrapingException {
        try {
            var headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            var requestEntity = new HttpEntity<>(
                    objectMapper.writeValueAsString(requestBody),
                    headers
            );

            var response = restTemplate.exchange(
                    apiUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode().is4xxClientError()) {
                log.warn("Server error: {} - body: {}", response.getStatusCode(), response.getBody());
                throw new InvalidScrapingException("Client error: {} " + response.getStatusCode());
            }

            if (response.getStatusCode().is5xxServerError()) {
                log.warn("Server error: {} - body: {}", response.getStatusCode(), response.getBody());
                throw new InvalidScrapingException("Server error: " + response.getStatusCode());
            }

            return objectMapper.readTree(response.getBody());

        } catch (RestClientException e) {
            var url = requestBody.has("url") ? requestBody.get("url").asText() : "unknown URL";
            if (isTechnicalError(e)) {
                log.warn("Technical error while calling Firecrawl API for URL '{}': {}", url, e.getMessage(), e);
                throw new InvalidScrapingException(e);
            } else {
                log.warn("Non-technical error while calling Firecrawl API for URL '{}': {}", url, e.getMessage(), e);
                return objectMapper.nullNode();
            }
        } catch (JsonProcessingException e) {
            var url = requestBody.has("url") ? requestBody.get("url").asText() : "unknown URL";
            log.warn("JSON processing error while calling Firecrawl API: URL '{}': {}", url, e.getMessage(), e);
            return objectMapper.nullNode();
        }
    }

    private boolean isTechnicalError(RestClientException e) {
        var statusCode = e instanceof RestClientResponseException rcre ? HttpStatus.valueOf(rcre.getRawStatusCode()) : HttpStatus.INTERNAL_SERVER_ERROR;
        return !statusCode.is4xxClientError() || statusCode.equals(HttpStatus.TOO_MANY_REQUESTS);
    }

    private final ApplicationContext applicationContext;

    @Override
    @Async
    public void scrapeOffers(ScrapeOffersCommandDTO command) {
        applicationContext.getBean(FirecrawlSynchronizer.class, new AtsGetOfferConfig()
                        .setAtsCode(FIRECRAWL_ATS_CODE)
                        .setConfigCode(command.getOrganizationCode())
                        .setRecruiterCode(command.getOrganizationCode())
                        .setRequiresConfirmation(false)
                        .setGlobalMaxNumberOfOffersAllowed(command.getMaxOffersScrapped())
                        .setLazyInitServiceName("firecrawlLazyInitService")
                        .setLimiterServiceName("globalLimiter")
                        .setRemoteUrl(command.getRecruiterUrl())
                        .setOfferUrlMustContain(command.getOfferUrlMustContain()),
                applicationContext.getBean("firecrawlJobParser")
        ).fetchAndUpdateOffers();
    }
}
