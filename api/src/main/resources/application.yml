spring:
  application:
    name: j<PERSON>suisPASunCV API
  devtools:
    restart:
      enabled: false
  main:
    allow-bean-definition-overriding: true
  liquibase:
    change-log: "classpath:db/changelog/db.changelog-master.xml"
    contexts: ${spring.profiles.active:default}
  datasource:
    url: jdbc:mariadb://${MYSQL_URL:mariadb.localhost}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:odas}
    username: ${MYSQL_USER:odas}
    password: ${MYSQL_PASSWORD:password}
    driverClassName: org.mariadb.jdbc.Driver
    hikari:
      maxLifetime: 590000
      maximumPoolSize: 35
      minimumIdle: 10
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
  servlet:
    multipart:
      max-file-size: 10MB
  jpa:
    database: MYSQL
    # Next line:
    # * tells Hibernate to use InnoDB and not MyISAM storage engine (since MySQL55Dialect)
    # * tells Hibernate to use MariaDB native sequences (since MariaDB103Dialect)
    database-platform: org.hibernate.dialect.MariaDBDialect
    # show-sql: true
    generate-ddl: false
    open-in-view: false
    hibernate:
      function_contributor: com.erhgo.config.MariaDBFunction
      ddl-auto: 'none'
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl
    properties:
      hibernate:
        timezone:
          default_storage: NORMALIZE
        type:
          preferred_uuid_jdbc_type: BINARY
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:DISABLED}
    vertex:
      ai:
        gemini:
          chat:
            options:
              enabled: false
              model: gemini-2.0-flash
          project-id: ${VERTEX_PROJECT_ID:jenesuispasuncv-api}
          location: europe-west1
          credentials-uri: ${VERTEX_CREDENTIALS:}
          transport: rest
server:
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  port: 8080
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css, application/javascript, application/json
    min-response-size: 1024

security:
  basic:
    enabled: false

logbook:
  exclude:
    - /actuator/health
    - /api/odas/user/front-office/export
    - /api/odas/candidature/common/export

sendinblue:
  maxRecipientsPerRequest: ${SENDINBLUE_MAX_RECIPIENTS:1000}
  apiKey: ${SENDIN_BLUE_API_KEY:}
  list:
    job-dating-optin-front-users: ${SENDINBLUE_JOB_DATING_LYON_LIST_ID:}
    default-new-user-list: ${SENDINBLUE_DEFAULT_NEW_USER_LIST_ID:346}
  templates:
    classical-candidature-proposal: ${SENDINBLUE_CANDIDATURE_PROPOSAL_TEMPLATE_ID:}
    validation-candidature: ${SENDINBLUE_CANDIDATURE_VALIDATION_TEMPLATE_ID:308}
    sourcing-candidature-proposal: ${SENDINBLUE_SOURCING_CANDIDATURE_PROPOSAL_TEMPLATE_ID:156}
    sourcing-offer-sum-up: ${SENDINBLUE_SOURCING_OFFER_SUM_UP_TEMPLATE_ID:543}
    candidature-notification: ${SENDINBLUE_CANDIDATURE_NOTIFICATION_TEMPLATE_ID:}
    sourcing-candidature-notification: ${SOURCING_CANDIDATURE_NOTIFICATION_TEMPLATE_ID:613}
    confirm-account: ${SENDINBLUE_CONFIRM_ACCOUNT_TEMPLATE_ID:}
    refuse-candidature-sourcing: ${SENDINBLUE_REFUSE_CANDIDATURE_SOURCING_TEMPLATE_ID:235}
    delete-account: ${SENDINBLUE_CONFIRM_DELETE_ACCOUNT_TEMPLATE_ID:232}
    confirm-account-fo-out-sector: ${SENDINBLUE_CONFIRM_ACCOUNT_FO_OUT_SECTOR_TEMPLATE_ID:268}
    contact-sourcing: ${SENDINBLUE_CONTACT_SOURCING_TEMPLATE_ID:163}
    technical-warning-sourcing: ${SENDINBLUE_TECHNICAL_WARNING_SOURCING_TEMPLATE_ID:219}
    invite-user-sourcing: ${SENDINBLUE_SEND_INVITATION_TEMPLATE_ID:220}
    remind-recruitment-to-recruiters: ${SENDINBLUE_REMIND_RECRUITMENTS_TO_RECRUITERS_TEMPLATE_ID:221}
    jd-confirm-inscription: ${SENDINBLUE_CONFIRM_JD:226}
    second-confirm-account-fo: ${SENDINBLUE_SECOND_CONFIRM_ACCOUNT:374}
    sourcing-warning-end-trial-users: ${SENDINBLUE_TRIAL_END_SOURCING_TEMPLATE_ID:312}
    sourcing-welcome-trial-users: ${SENDINBLUE_WELCOME_TRIAL_TEMPLATE_ID:318}
    close-recruitment-fo-notification: ${FO_CLOSE_RECRUITMENT_TEMPLATE:415}
    suspend-sourcing-recruitment-after-end-date: ${SUSPEND_SOURCING_RECRUITMENT_AFTER_END_DATE_TEMPLATE_ID:422}
    sourcing-spontaneous-candidature-notification: ${SOURCING_SPONTANEOUS_CANDIDATURE_TEMPLATE_ID:472}
    suspend-recruitment-candidature-notification: ${SUSPEND_RECRUITMENT_CANDIDATURE_TEMPLATE_ID:565}
    handicap-account-creation-with-profile: ${HANDICAP_CREATION_WITH_PROFILE_TEMPLATE_ID:614}
    handicap-account-creation-without-profile: ${HANDICAP_CREATION_WITHOUT_PROFILE_TEMPLATE_ID:619}
  supportEmail: ${SUPPORT_EMAIL:<EMAIL>}
  jobOfferSender: ${JOB_OFFER_SENDER:<EMAIL>}
  forcedSourcingRecipients: ${FORCED_SOURCING_RECIPIENTS:<EMAIL>}
keycloak:
  enabled: true
  ssl-required: external
  resource: odas-back-office-api
  bearer-only: true
  use-resource-role-mappings: false
  principal-attribute: user_id
  auth-server-url: ${KEYCLOAK_URL:http://auth.localhost}
  credentials:
    secret: ${KEYCLOAK_SECRET:11ba534d-8d48-4400-8eed-e391a7965ecc}

keycloak-realms:
  auth-server-url: ${KEYCLOAK_URL:http://auth.localhost}
  sourcing_realm_id: sourcing.erhgo.app
  front_office_realm_id: erhgo.app
  back_office_realm_id: bo.erhgo.app
  api_base_url: ${API_BASE_URL:http://localhost:8080}
  front_api_client_secret: ${KEYCLOAK_FRONT_API_CLIENT_SECRET:1f1dc79f-a0e0-437e-b60e-02d7fa3b74fe}
  back_api_client_secret: ${KEYCLOAK_BACK_API_CLIENT_SECRET:4e495995-01e1-479a-947b-8cd4f583096d}
  sourcing_api_client_secret: ${KEYCLOAK_SOURCING_API_CLIENT_SECRET:4e495995-01e1-479a-947b-8cd4f583096d}
  password_policy: length(8) and digits(1) and notUsername(undefined) and upperCase(1) and lowerCase(1)
  front_office_client_id: web-front
  back_office_client_id: web
  sourcing_client_id: web-sourcing
  google_client_id: ${KEYCLOAK_GOOGLE_CLIENT_ID:dummy}
  google_client_secret: ${KEYCLOAK_GOOGLE_CLIENT_SECRET:dummy}
  apple_client_id: ${KEYCLOAK_APPLE_CLIENT_ID:dummy}
  apple_key_id: ${KEYCLOAK_APPLE_KEY_ID:dummy}
  apple_team_id: ${KEYCLOAK_APPLE_TEAM_ID:dummy}
  apple_p8_key: ${KEYCLOAK_APPLE_P8_KEY:dummy}}
ai-generation:
  behavior:
    temperature: 0.7
    model: gpt-4.1-mini
    maxTokens: 2000
    maxRetry: 3
    messageFilename: BehaviorsQualificationMessages.yaml
  classification:
    temperature: 0.7
    model: gpt-4.1-mini
    maxTokens: 2000
    maxRetry: 3
    messageFilename: ClassificationMessages.yaml
  rome:
    temperature: 0.7
    model: gpt-4.1
    maxTokens: 2000
    maxRetry: 3
    messageFilename: RomeMessages.yaml
  activity:
    temperature: 1
    model: gpt-4.1
    maxTokens: 4096
    forceJson: true
    maxRetry: 3
    messageFilename: ActivityMessages.yaml
  occupation-description:
    temperature: 0.7
    model: gpt-4.1-mini
    maxTokens: 800
    maxRetry: 3
    messageFilename: DescriptionMessages.yaml
  behavior-description:
    temperature: 0.7
    model: gpt-4.1-mini
    maxTokens: 800
    maxRetry: 3
    messageFilename: BehaviorsDescriptionMessages.yaml
  user-behavior-description:
    temperature: 0.7
    model: gpt-4.1-mini
    maxTokens: 200
    maxRetry: 3
    messageFilename: UserBehaviorDescriptionMessages.yaml
  level:
    temperature: 0.7
    model: gpt-4.1-mini
    maxTokens: 2000
    maxRetry: 3
    messageFilename: MasteryLevelMessages.yaml
  title:
    temperature: 0.7
    model: gpt-4.1-mini
    maxTokens: 800
    maxRetry: 3
    messageFilename: OccupationTitleMessages.yaml
  best-matching-occupation-label:
    temperature: 0.7
    model: gpt-4.1
    maxTokens: 2000
    maxRetry: 3
    messageFilename: FindBestMatchingOccupationMessages.yaml
    forceJson: true
  user-experiences:
    temperature: 0.7
    model: gpt-4.1
    maxTokens: 4000
    maxRetry: 1
    messageFilename: UserExperienceExtractionMessages.yaml
    forceJson: true
  user-experiences-vision:
    temperature: 0.7
    model: gpt-4.1
    maxTokens: 4000
    maxRetry: 1
    messageFilename: UserExperienceExtractionMessages.yaml
    forceJson: true
  user-infos:
    temperature: 0.7
    model: gpt-4.1
    maxTokens: 4000
    maxRetry: 1
    messageFilename: UserInfosExtractionMessages.yaml
    forceJson: true
  user-infos-vision:
    temperature: 0.7
    model: gpt-4.1
    maxTokens: 4000
    maxRetry: 1
    messageFilename: UserInfosExtractionMessages.yaml
    forceJson: true
  hashtags:
    temperature: 1
    model: gpt-4.1
    #provider: vertex
    #model: gemini-2.0-flash
    maxTokens: 1800
    maxRetry: 1
    messageFilename: HashtagsMessages.yaml
  softSkillsDescriptions:
    temperature: 0.5
    model: gpt-4.1
    maxTokens: 3000
    maxRetry: 1
    messageFilename: SoftSkillsDescriptionMessages.yaml
    forceJson: true
  hardSkills:
    temperature: 1
    model: gpt-4.1
    maxTokens: 3000
    maxRetry: 1
    messageFilename: HardSkillsMessages.yaml
    forceJson: true
  hardSkillsVision:
    temperature: 0.7
    model: gpt-4.1
    maxTokens: 3000
    maxRetry: 1
    messageFilename: HardSkillsMessages.yaml
    forceJson: true
    visionSupport: true
  formatCovea:
    temperature: 1
    model: gpt-4.1-mini
    maxTokens: 10000
    maxRetry: 1
    messageFilename: FormatTextToHtml.yaml
smtp:
  password: ${SMTP_PASSWORD}
  starttls: ${SMTP_ENABLE_STARTTLS:false}
  auth: ${SMTP_ENABLE_AUTH:false}
  port: ${SMTP_PORT:1025}
  host: ${SMTP_HOST:mailhog}
  from: "<EMAIL>"
  from_display_Name: "Contact #jenesuisPASunCV"
  ssl: ${SMTP_ENABLE_SSL:false}
  user: ${SMTP_USER:<EMAIL>}

slack:
  token: ${SLACK_TOKEN:}
  url-for-message-type:
    DataHealthCheckMessage: ${SLACK_DATA_HEALTH_CHECK_NOTIFICATION_CHANNEL_ID:C04TPEQ636C}
    EmailSendingNotifierMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C06PA2UG4ES}
    ExperienceOnUnqualifiedOccupationMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C06PA2UG4ES}
    UnqualifiedCreatedOccupationMessageDTO: ${SLACK_UNQUALIFIED_CREATED_OCCUPATION_NOTIFICATION_CHANNEL_ID:C0735N96J6N}
    AddedAlternativeLabelMessageDTO: ${SLACK_UNQUALIFIED_CREATED_OCCUPATION_NOTIFICATION_CHANNEL_ID:C0735N96J6N}
    FrontofficeAccountDeletionNotifierMessageDTO: ${SLACK_USER_DELETION_NOTIFICATION_CHANNEL_ID:C050ZDA4SUA}
    FrontofficeNotifierMessageDTO: ${SLACK_USER_DELETION_NOTIFICATION_CHANNEL_ID:C01F3P7SJ48}
    FrontOfficePersonalEmailDomainMessageDTO: ${SLACK_USER_PERSONAL_EMAIL_DOMAIN_LIST_CHANNEL_ID:C050ZDA4SUA}
    SourcingDisabledAccountMessageDTO: ${SLACK_SOURCING_USER_NOTIFICATION_CHANNEL_ID:C041H565XMK}
    SourcingInvitationUsedMessageDTO: ${SLACK_SOURCING_USER_NOTIFICATION_CHANNEL_ID:C041H565XMK}
    SourcingJobOnOccupationMessage: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C06PA2UG4ES}
    SourcingNewUserMessageDTO: ${SLACK_SOURCING_USER_NOTIFICATION_CHANNEL_ID:C041H565XMK}
    SourcingPublishRecruitmentMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C06PA2UG4ES}
    RemoteOfferMessageDTO: ${SLACK_REMOTE_OFFER_CHANNEL_ID:C07Q5K29P5K}
    RemoteOfferErrorDTO: ${SLACK_REMOTE_OFFER_CHANNEL_ID:C06PA2UG4ES}
    CSVImportEndsMessageDTO: ${SLACK_CSV_CHANNEL_ID:C08C34Y0C2E}
    CSVImportStartsMessageDTO: ${SLACK_CSV_CHANNEL_ID:C08C34Y0C2E}
    ATSRecruiterCreated: ${SLACK_ATS_RECRUITER_CHANNEL_ID:C08C5LLG59S}
keycloak-admin:
  log: ${KEYCLOAK_ADMIN_USERNAME:admin}
  pwd: ${KEYCLOAK_ADMIN_PASSWORD:Pa55w0rd}

algolia:
  applicationId: ${ALGOLIA_APPLICATION_ID:4T5UZKI6NQ}
  adminApiKey: ${ALGOLIA_ADMIN_API_KEY:}
  searchApiKey: ${ALGOLIA_SEARCH_API_KEY:}
  indexPrefix: ${ALGOLIA_INDEX_PREFIX:${spring.profiles.active}}
  maxCandidates: ${ALGOLIA_MAX_CANDIDATES:1000}

trimoji:
  requestJsonTemplate: ${TRIMOJI_JSON_TEMPLATE:}
  apiKey: ${TRIMOJI_API_KEY:}
  urlJsonPath: ${TRIMOJI_TEST_URL_JSONPATH:$.data.link}
  url: ${TRIMOJI_URL:https://integration.trimoji.fr/api/v1/workflow/initTest}
  callbackUrl: ${TRIMOJI_CALLBACK_URL:http://localhost:8080/api/odas/public/trimoji}

sourcing:
  capacityTolerance: ${SOURCING_CAPACITY_TOLERANCE:0.90}
  maxOffersPerCandidate: ${MAX_OFFER_PER_CANDIDATE_IN_MAIL:50}
  maxOffersPerRecruiter: ${MAX_OFFER_PER_RECRUITER_IN_MAIL:20}
  salaryMinToleranceRatio: ${SALARY_MIN_TOLERANCE_RATIO:0.7}
  salaryMaxToleranceRatio: ${SALARY_MAX_TOLERANCE_RATIO:1.3}
  topTenUpperDelta: ${TOP_TEN_UPPER_DELTA:0.5}
  topTenLowerDelta: ${TOP_TEN_LOWER_DELTA:0.2}
quickemailverification:
  apiKey: ${EMAIL_VERIFICATION_API_KEY:}
  url: https://api.quickemailverification.com/v1/verify
  timeout-in-ms:
    connect: 3000
    read: 3000

insee:
  apiKey: ${INSEE_API_KEY:}
  url: https://api.insee.fr/api-sirene/3.11/siret/
  timeout-in-ms:
    connect: 3000
    read: 3000

firebase:
  apiKey: ${FIREBASE_API_KEY:}
gmail:
  apiKey: ${GMAIL_API_KEY:}
  applicationName: ${GMAIL_APPLICATION_NAME:erhgo-smtp}
  senderEmail: ${GMAIL_SENDER_EMAIL:<EMAIL>}
  cvRecipients: ${CV_RECIPIENTS:<EMAIL>}
firecrawl:
  api-key: ${FIRECRAWL_API_KEY:fc-d1234567890abcdef1234567890abcdef}
  api-url: ${FIRECRAWL_API_URL:https://api.firecrawl.dev/v1/scrape}
  timeout-in-ms:
    connect: 10000
    read: 120000

application:
  cache-warmup:
    delay: ${USER_CACHE_WARMUP_DELAY:PT1H}
  executeDataHealthCheckQueries:
    cron: ${EXECUTE_DATA_HEALTH_CHECK_QUERIES_CRON:0 0 7,11,16 * * *}
  sendRecruitmentProposalMails:
    cron: ${SEND_RECRUITMENT_PROPOSAL_CRON:0 15 8 * * *}
  archiving:
    cron: ${ARCHIVING_REFUSED_CANDIDATURES_CRON:0 35 4 * * *}
    numberOfMonthsToArchiveRefusedCandidatures: ${NUMBER_OF_MONTHS_TO_ARCHIVE_REFUSED_CANDIDATURE:3}
    numberOfMonthsToArchiveUntreatedCandidatures: ${NUMBER_OF_MONTHS_TO_ARCHIVE_REFUSED_CANDIDATURE:6}
    numberOfMonthsToArchiveTreatedCandidatures: ${NUMBER_OF_MONTHS_TO_ARCHIVE_REFUSED_CANDIDATURE:12}
  index_user:
    cron: ${INDEX_USER_CRON:0 15 * * * *}
    max_number_of_indexations_per_hour: ${MAX_NUMBER_OF_INDEXATIONS_PER_HOUR:1000}
    user_indexation_delay_in_seconds: ${USER_INDEXATION_INTERVAL:3600}
  mailing:
    blacklistRefreshCron: ${MAIL_BLACKLIST_REFRESH_CRON:0 0 */3 * * *}
    newUserSendWelcomeMailCron: ${MAIL_NEW_USER_SEND_CRON:0 0 * * * *}
  mobile:
    tokenLifetimeInMonths: ${MOBILE_TOKEN_LIFETIME:3}
    tokensRefreshCron: ${MOBILE_TOKENS_REFRESH_CRON:0 42 * * * *}
    notificationTestCron: ${MOBILE_NOTIFICATION_TEST_CRON:0 0 12 * * MON}
    notificationTesters: ${MOBILE_NOTIFICATION_TESTERS:}
    forcedNotificationRecipientUserId: ${FORCED_NOTIFICATION_RECIPIENT_USER_ID:}
  immediateCandidatureNotification:
    cron: ${IMMEDIATE_CANDIDATURE_NOTIFICATION_CRON:0 0 */1 * * *}
  notesLifespanInMonth: ${NOTES_LIFESPAN_IN_MONTH:6}
  notificationCleanup:
    cron: ${NOTIFICATION_CLEANUP_CRON:0 0 4 * * *}
    retentionInMonths: ${NOTIFICATION_CLEANUP_RETENTION_MONTHS:3}
  occupation-relative-url: /repository/erhgo-occupation/%s
  organization-relative-url: /setup/organization/edit/%s
  external-offer-relative-url: /#/edit-recruitment?externalOfferId=%s
  sourcing-recruitment-relative-url: /#/recruitment-detail/%d
  sourcing-candidature-relative-url: /#/candidature-detail/%d
  profile-relative-url: /repository/front-users/%s/detail
  recruitment-relative-url: /setup/organization/%s/recruitment/edit/%s
  remind:
    cron: ${MAIL_REMIND_CRON:0 30 */3 * * *}
  sendRecruitmentNotifications:
    cron: ${SEND_RECRUITMENT_MAIL_CRON:0 15 */2 * * *}
  sendReminderRecruitmentMails:
    cron: ${SEND_REMINDER_RECRUITMENT_MAIL_CRON:0 0 8 * * *}
  sendSourcingTrialMails:
    cron: ${SEND_SOURCING_TRIAL_MAILS:0 30 8 * * *}
  resendNotificationsForOpenRecruitments:
    cron: ${RESEND_NOTIFICATIONS_FOR_OPEN_RECRUITMENTS_CRON:0 0 5 * * *}
  simon:
    config: ${SIMON_AOP_CONFIG:execution(* com.erhgo..*(..)) || execution(* org.keycloak.admin.client..*(..))}
  sourcing:
    nbDaysBeforeSendingWarningEndOfTrial: ${SOURCING_DELAY_WARNING_END_OF_TRIAL:23}
    nbDaysBeforeSendingWelcome: ${SOURCING_DELAY_WELCOME_MAIL_SEND:1}
    recruitmentLifespanInDays: ${RECRUITMENT_DURATION_IN_DAYS:28}
    trialDurationInDays: ${SOURCING_TRIAL_DURATION_IN_DAYS:30}
  suspendSourcingRecruitments:
    cron: ${SUSPEND_RECRUITMENTS_CRON:0 30 4 * * *}
  updateObsoleteNotes:
    cron: ${UPDATE_OBSOLETE_NOTES_CRON:0 0 3 * * *}
  sectorDepartmentsCode: 01,03,07,08,10,15,21,25,26,38,39,42,43,51,52,54,55,57,58,63,67,68,69,70,71,73,74,88,89,90
  sendFOWelcomeMailDelayInHours: ${FO_WELCOME_MAIL_DELAY_IN_HOURS:6}
  sendFOSecondWelcomeMailDelayInHours: ${FO_WELCOME_SECOND_MAIL_DELAY_IN_HOURS:48}
  cors:
    allowedOriginPatterns: https://*.erhgo.fr,https://*.jenesuispasuncv.fr,https://*.jenesuispasunhandicap.fr,http://localhost:*,http://auth-localhost,http://*-e2e:8080,http://*-e2e.localhost:8080,http://*-e2e.localhost,http://127.0.0.1:*
  socialLogoUrl: ${SOCIAL_LOGO_URL:https://jenesuispasuncv.fr/wp-content/uploads/2020/09/bloc-marque-je-ne-suis-pas-un-cv.png}
  socialLogoHandicapUrl: ${SOCIAL_LOGO_HANDICAP_URL:https://jenesuispasuncv.fr/wp-content/uploads/2025/04/<EMAIL>}
  handicapFoUrl: ${HANDICAP_FO_URL:https://www.jenesuispasunhandicap.fr}
ats:
  cronFirstPass: ${ANALYSE_XML_DATA_FOR_EXTERNAL_OFFERS_CRON_1:0 25 6 * * *}
  cronSecondPass: ${ANALYSE_XML_DATA_FOR_EXTERNAL_OFFERS_CRON_2:0 25 12 * * *}
  eolia:
    fetch:
      - atsCode: eolia
        recruiterCode: ${EOLIA_MB_RECRUITER_CODE:S-21663}
        remoteUrl: https://cloud.eolia-software.com/ws_martinbelaysoud_77B2C103-8A58-409A-BE5F-65239AD6A30D_fr_jobboard_entreprise.asp
        rootPath: /data/job
        configCode: MB
      - atsCode: eolia
        recruiterCode: ${EOLIA_EMALEC_RECRUITER_CODE:S-21544}
        remoteUrl: https://cloud.eolia-software.com/ws_emalec_08D8235D-FFD1-49E9-B98D-59464637C012_jobboard_wp.asp
        rootPath: /data/job
        configCode: EMALEC
      - atsCode: eolia
        recruiterCode: ${EOLIA_SERFIM_RECRUITER_CODE:S-0477}
        remoteUrl: https://cloud.eolia-software.com/ws_serpollet_fr_jobboard_AC1F15CD-53E4-4ABE-9471-F9DF88D7E31E.asp
        rootPath: /data/job
        configCode: SERFIM
    send:
      - atsCode: eolia
        recruiterCode: ${EOLIA_MB_RECRUITER_CODE:S-21663}
        candidatureNotificationMail: ${EOLIA_MB_CANDIDATURE_MAIL:<EMAIL>}
        numberOfDaysToConsider: ${EOLIA_NB_DAYS:10}
        delayInMinutes: ${EOLIA_DELAY_IN_MINUTES:60}
      - atsCode: eolia
        recruiterCode: ${EOLIA_EMALEC_RECRUITER_CODE:S-21544}
        candidatureNotificationMail: ${EOLIA_EMALEC_CANDIDATURE_MAIL:<EMAIL>}
        numberOfDaysToConsider: ${EOLIA_NB_DAYS:10}
        delayInMinutes: ${EOLIA_DELAY_IN_MINUTES:60}
      - atsCode: eolia
        recruiterCode: ${EOLIA_SERFIM_RECRUITER_CODE:S-0477}
        candidatureNotificationMail: ${EOLIA_SERFIM_CANDIDATURE_MAIL:<EMAIL>}
        numberOfDaysToConsider: ${EOLIA_NB_DAYS:10}
        delayInMinutes: ${EOLIA_DELAY_IN_MINUTES:60}
  digital-recruiters:
    fetch:
      - remoteUrl: https://app.digitalrecruiters.com/export/job-ads/nJLTxDP4QtcuL4RDjHYZM1jktr5gVuH2kB8GE888
        recruiterCode: ${DR_APRIL_RECRUITER_CODE:S-21531}
        atsCode: DIGITAL_RECRUITERS
        configCode: APRIL
      - remoteUrl: https://app.digitalrecruiters.com/export/job-ads/jU7KXIlQnsUXVoq8FrDKCG2hWwx6P0XOlfBaZ9NY
        recruiterCode: ${DR_CEGID_RECRUITER_CODE:S-21651}
        atsCode: DIGITAL_RECRUITERS
        configCode: CEGID
      - remoteUrl: https://app.digitalrecruiters.com/export/job-ads/nGXXgbQ5SE8hUTsJcqSAZ3tUI0iDjJUoAXCKIcBd
        recruiterCode: ${DR_FONCIA_RECRUITER_CODE:S-21679}
        atsCode: DIGITAL_RECRUITERS
        configCode: FONCIA
      - remoteUrl: https://app.digitalrecruiters.com/export/job-ads/Pgi5ptMqt27UJUflL9MJ1hmmZNVTImLqHxOaagMx
        recruiterCode: ${DR_EFFICITY_RECRUITER_CODE:S-21879}
        atsCode: DIGITAL_RECRUITERS
        configCode: EFFICITY
      - remoteUrl: https://app.digitalrecruiters.com/export/job-ads/PRVZCE5qyHt5VQANco7h1ByYB8FhflRUpbulspBf
        recruiterCode: ${DR_KEOLIS_LYON_RECRUITER_CODE:S-21436}
        atsCode: DIGITAL_RECRUITERS
        configCode: KEOLIS_LYON
    send:
      - atsCode: DIGITAL_RECRUITERS
        recruiterCode: ${DR_APRIL_RECRUITER_CODE:S-21531}
        # prod: https://app.digitalrecruiters.com/api/candidate/apply/nJLTxDP4QtcuL4RDjHYZM1jktr5gVuH2kB8GE888
        candidatureNotificationUrl: ${DR_APRIL_SEND_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud/}
        numberOfDaysToConsider: ${APRIL_NB_DAYS:10}
        delayInMinutes: ${APRIL_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${DR_APRIL_API_KEY:42}
      - atsCode: DIGITAL_RECRUITERS
        recruiterCode: ${DR_CEGID_RECRUITER_CODE:S-21651}
        # prod: https://app.digitalrecruiters.com/api/candidate/apply/jU7KXIlQnsUXVoq8FrDKCG2hWwx6P0XOlfBaZ9NY
        candidatureNotificationUrl: ${DR_CEGID_SEND_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud/}
        numberOfDaysToConsider: ${CEGID_NB_DAYS:10}
        delayInMinutes: ${CEGID_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${DR_CEGID_API_KEY:42}
      - atsCode: DIGITAL_RECRUITERS
        recruiterCode: ${DR_FONCIA_RECRUITER_CODE:S-21679}
        # prod: https://app.digitalrecruiters.com/api/candidate/apply/qYgSmhNV2JjrqBNHKgUvUf0BUqPoY2pmcZlyKJOb
        candidatureNotificationUrl: ${DR_FONCIA_SEND_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud/}
        numberOfDaysToConsider: ${CEGID_NB_DAYS:10}
        delayInMinutes: ${CEGID_DELAY_IN_MINUTES:60}
        # prod: qYgSmhNV2JjrqBNHKgUvUf0BUqPoY2pmcZlyKJOb
        candidatureNotificationApiKey: ${DR_FONCIA_API_KEY:42}
      - atsCode: DIGITAL_RECRUITERS
        recruiterCode: ${DR_EFFICITY_RECRUITER_CODE:S-21879}
        # prod: https://app.digitalrecruiters.com/api/candidate/apply/Pgi5ptMqt27UJUflL9MJ1hmmZNVTImLqHxOaagMx
        candidatureNotificationUrl: ${DR_EFFICITY_SEND_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud/}
        numberOfDaysToConsider: ${CEGID_NB_DAYS:10}
        delayInMinutes: ${CEGID_DELAY_IN_MINUTES:60}
        # prod: Pgi5ptMqt27UJUflL9MJ1hmmZNVTImLqHxOaagMx
        candidatureNotificationApiKey: ${DR_EFFICITY_API_KEY:42}
  adecco:
    forcedSlackChannel: ${ADECCO_FORCED_SLACK_CHANNEL:C06PA2UG4ES}
    fetch:
      atsCode: adecco
      recruiterCode: ${ADECCO_RECRUITER_CODE:S-21712}
      # test: https://cd-adecco-fr.uat.cms.adecco.com/resultats-offres-emploi/c-cdd/d-<DPT>?buname=adecco.fr%7cadeccopme.fr%7c&rss=1&employmenttype=ADCFREMP004
      # prod: https://www.adecco.fr/resultats-offres-emploi/c-cdd/d-<DPT>?buname=adecco.fr%7cadeccopme.fr%7c&rss=1&employmenttype=ADCFREMP004
      remoteUrlTemplate: ${ADECCO_FETCH_URL:https://www.adecco.fr/resultats-offres-emploi/c-cdd/d-<DPT>?buname=adecco.fr%7cadeccopme.fr%7c&rss=1&employmenttype=ADCFREMP004}
      # prod: Ain|Aisne|Allier|Alpes-Maritimes|Ardèche|Ardennes|Ariège|Aube|Aude|Aveyron|Bas-Rhin|Bouches-Du-Rhône|Calvados|Cantal|Charente|Charente-Maritime|Cher|Corrèze|Côte-D'Or|Côtes-D'Armor|Creuse|Deux-Sèvres|Dordogne|Doubs|Drôme|Essonne|Eure|Eure-Et-Loir|Finistère|Gard|Gers|Gironde|Guadeloupe &amp; St Martin &amp; St Barthelemy|Haute-Garonne|Haute-Loire|Haute-Marne|Hautes-Alpes|Haute-Saône|Haute-Savoie|Hautes-Pyrénées|Haute-Vienne|Haut-Rhin|Hauts-De-Seine|Hérault|Ille-Et-Vilaine|Indre|Indre-Et-Loire|Isère|Jura|La-Réunion|Landes|Loire|Loire-Atlantique|Loiret|Loir-Et-Cher|Lot|Lot-Et-Garonne|Lozère|Maine-Et-Loire|Manche|Marne|Mayenne|Meurthe-Et-Moselle|Meuse|Morbihan|Moselle|Nièvre|Nord|Oise|Orne|Paris|Pas-De-Calais|Puy-De-Dôme|Pyrénées-Atlantiques|Pyrénées-Orientales|Rhône|Saône-Et-Loire|Sarthe|Savoie|Seine-Et-Marne|Seine-Maritime|Seine-Saint-Denis|Somme|Tarn|Tarn-Et-Garonne|Territoire-De-Belfort|Val-De-Marne|Val-D'Oise|Var|Vaucluse|Vendée|Vienne|Vosges|Yonne|Yvelines
      departments: ${ADECCO_DEPARTMENTS:Vaucluse|Rhône}
      rootPath: /rss/channel/item
      forcedSlackChannel: ${ADECCO_FORCED_SLACK_CHANNEL:C06PA2UG4ES}
    send:
      - atsCode: adecco
        recruiterCode: ${ADECCO_RECRUITER_CODE:S-21712}
        candidatureNotificationUrl: ${ADECCO_SEND_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud}
        numberOfDaysToConsider: ${ADECCO_NB_DAYS:10}
        delayInMinutes: ${ADECCO_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${ADECCO_API_KEY:60}
  success-factors:
    fetch:
      - remoteUrl: https://careers.keolis.com/feed/393933
        rootPath: /source/job
        atsCode: SUCCESS_FACTORS
        recruiterProvider: PerLocationFetcher
        configCode: KEOLIS
        limiterServiceName: perRecruiterLimiter
  default:
    send:
      - atsCode: SUCCESS_FACTORS
        numberOfDaysToConsider: ${HW_NB_DAYS:10}
        delayInMinutes: ${HW_DELAY_IN_MINUTES:60}
        recruitersProvider: PerDBConfigSender
        recruitersProviderConfigCode: KEOLIS
      - atsCode: DIGITAL_RECRUITERS
        recruiterCode: ${DR_KEOLIS_LYON_RECRUITER_CODE:S-21436}
        numberOfDaysToConsider: ${CEGID_NB_DAYS:45}
        delayInMinutes: ${CEGID_DELAY_IN_MINUTES:60}
  hello-work:
    fetch:
      - remoteUrl: ${HW_MLOGISTICS_FETCH_URL:https://www.hw-recruteur.com/flux/v1/share/jobboard/custom/WJA80Rn4rJRfU6HES9KrKQ==/Osa3C3JoARObf6I4NFc-sA==}
        recruiterCode: ${HW_MLOGISTICS_RECRUITER_CODE:S-21723}
        atsCode: HELLO_WORK
        configCode: MLOGISTICS
      - remoteUrl: ${HW_MDA_FETCH_URL:https://www.hw-recruteur.com/flux/v1/share/jobboard/custom/h2P-yShdHXCrH5-xp75Lzg==/SFwkRTJyHHg-EckTjk_ZZQ==}
        recruiterCode: ${HW_MDA_RECRUITER_CODE:S-21717}
        atsCode: HELLO_WORK
        configCode: MDA
      - remoteUrl: ${HW_SACVL_FETCH_URL:https://www.hw-recruteur.com/flux/v1/share/jobboard/custom/tergnNzZ89QeGjl896eZOA==/iCUVVc2vyrrXvMIXbcgwTQ==}
        recruiterCode: ${HW_SACVL_RECRUITER_CODE:S-21594}
        atsCode: HELLO_WORK
        configCode: SACVL
      - remoteUrl: ${HW_BEL_FETCH_URL:https://www.hw-recruteur.com/flux/v1/share/jobboard/custom/sIGMcK4Nhxji8ETUzirDwA==/P-Eltrt9q-Ykyq1b06LU2A==}
        recruiterCode: ${HW_BEL_RECRUITER_CODE:S-21614}
        atsCode: HELLO_WORK
        configCode: BEL
    send:
      - atsCode: HELLO_WORK
        recruiterCode: ${HW_MLOGISTICS_RECRUITER_CODE:S-21723}
        candidatureNotificationMail: ${HW_MLOGISTICS_FORCED_EMAIL:<EMAIL>}
        numberOfDaysToConsider: ${HW_NB_DAYS:10}
        delayInMinutes: ${HW_DELAY_IN_MINUTES:60}
      - atsCode: HELLO_WORK
        recruiterCode: ${HW_MDA_RECRUITER_CODE:S-21717}
        candidatureNotificationMail: ${HW_MDA_FORCED_EMAIL:<EMAIL>}
        numberOfDaysToConsider: ${HW_NB_DAYS:10}
        delayInMinutes: ${HW_DELAY_IN_MINUTES:60}
      - atsCode: HELLO_WORK
        recruiterCode: ${HW_SACVL_RECRUITER_CODE:S-21594}
        candidatureNotificationMail: ${HW_SACVL_FORCED_EMAIL:<EMAIL>}
        numberOfDaysToConsider: ${HW_NB_DAYS:10}
        delayInMinutes: ${HW_DELAY_IN_MINUTES:60}
      - atsCode: HELLO_WORK
        recruiterCode: ${HW_BEL_RECRUITER_CODE:S-21614}
        candidatureNotificationMail: ${HW_BEL_FORCED_EMAIL:<EMAIL>}
        numberOfDaysToConsider: ${HW_NB_DAYS:10}
        delayInMinutes: ${HW_DELAY_IN_MINUTES:60}
  softy:
    fetch:
      - atsCode: softy
        remoteUrl: ${SOFTY_FETCH_URL:http://multidiffusion.softy.pro/jenesuispasuncv/jenesuispasuncv.xml}
        rootPath: /jobs/job
        recruiterProvider: PerOfferFetcher
        limiterServiceName: perOfferConfigCodeAndAuraInOutLimiter
    send:
      - atsCode: softy
        recruiterCode: ${SOFTY_MTAG_RECRUITER_CODE:S-21691}
        candidatureNotificationUrl: ${SOFTY_SEND_CANDIDATURES_URL:https://erhgo-softy.wiremockapi.cloud/candidature} # prod: https://sc.softy.pro/api/v1/jenesuispasuncv/apply
        candidatureNotificationApiKey: ${SOFTY_API_KEY:xxx}
        numberOfDaysToConsider: ${SOFTY_NB_DAYS:10}
        delayInMinutes: ${SOFTY_DELAY_IN_MINUTES:60}
      - atsCode: softy
        recruiterCode: ${SOFTY_ARES_RECRUITER_CODE:S-21786}
        candidatureNotificationUrl: ${SOFTY_SEND_CANDIDATURES_URL:https://erhgo-softy.wiremockapi.cloud/candidature} # prod: https://sc.softy.pro/api/v1/jenesuispasuncv/apply
        candidatureNotificationApiKey: ${SOFTY_API_KEY:xxx}
        numberOfDaysToConsider: ${SOFTY_NB_DAYS:10}
        delayInMinutes: ${SOFTY_DELAY_IN_MINUTES:60}
  beetween:
    fetch:
      - atsCode: beetween
        recruiterCode: ${BEETWEEN_JP_RECRUITER_CODE:S-21716}
        remoteUrl: ${BEETWEEN_JP_REMOTE_URL:https://partners.beetween.com/WeaselWeb/xen/feed/generic?on=jenesuispasuncv-***********&opt_accountId=***********}
        rootPath: /root/job
        configCode: JP
      - atsCode: beetween
        recruiterCode: ${BEETWEEN_RAS_RECRUITER_CODE:S-21575}
        remoteUrl: ${BEETWEEN_RAS_REMOTE_URL:https://partners.beetween.com/WeaselWeb/xen/feed/generic?on=jenesuispasuncv-***********&opt_accountId=***********}
        rootPath: /root/job
        configCode: RAS
      - atsCode: beetween
        recruiterCode: ${BEETWEEN_JOA_RECRUITER_CODE:S-21572}
        remoteUrl: ${BEETWEEN_JOA_REMOTE_URL:https://partners.beetween.com/WeaselWeb/xen/feed/generic?on=jenesuispasuncv-***********&opt_accountId=***********}
        rootPath: /root/job
        configCode: JOA
    send:
      - atsCode: beetween
        recruiterCode: ${BEETWEEN_JP_RECRUITER_CODE:S-21716}
        # prod: https://api.beetween.com/WeaselWeb/api/jobs/application
        candidatureNotificationUrl: ${BEETWEEN_SEND_CANDIDATURES_URL:https://erhgo-beetween.wiremockapi.cloud/candidature}
        numberOfDaysToConsider: ${BEETWEEN_NB_DAYS:10}
        delayInMinutes: ${BEETWEEN_DELAY_IN_MINUTES:1}
        candidatureNotificationApiKey: 5ju
      - atsCode: beetween
        recruiterCode: ${BEETWEEN_RAS_RECRUITER_CODE:S-21575}
        # prod: https://api.beetween.com/WeaselWeb/api/jobs/application
        candidatureNotificationUrl: ${BEETWEEN_SEND_CANDIDATURES_URL:https://erhgo-beetween.wiremockapi.cloud/candidature}
        numberOfDaysToConsider: ${BEETWEEN_NB_DAYS:10}
        delayInMinutes: ${BEETWEEN_DELAY_IN_MINUTES:1}
        candidatureNotificationApiKey: 5ju
      - atsCode: beetween
        recruiterCode: ${BEETWEEN_JOA_RECRUITER_CODE:S-21572}
        # prod: https://api.beetween.com/WeaselWeb/api/jobs/application
        candidatureNotificationUrl: ${BEETWEEN_SEND_CANDIDATURES_URL:https://erhgo-beetween.wiremockapi.cloud/candidature}
        numberOfDaysToConsider: ${BEETWEEN_RAS_NB_DAYS:10}
        delayInMinutes: ${BEETWEEN_DELAY_IN_MINUTES:1}
        candidatureNotificationApiKey: 5ju
        spontaneousCandidatureNotificationEmail: ${BEETWEEN_JOA_SPONTANEOUS_CANDIDATURE_EMAIL:<EMAIL>}
  taleez:
    fetch:
      - atsCode: taleez
        remoteUrl: ${TALEEZ_CONDAT_REMOTE_URL:https://taleez.com/feeds/jenesuispasuncv_fqsy6r9.xml}
        rootPath: /jobs/job
        recruiterProvider: PerOfferFetcher
        limiterServiceName: perOfferConfigCodeAndAuraInOutLimiter
    send:
      - atsCode: taleez
        recruiterCode: ${TALEEZ_CONDAT_RECRUITER_CODE:S-21690}
        candidatureNotificationUrl: ${TALEEZ_SEND_CANDIDATURES_URL:https://demo.taleez.com/api/public/applications} # prod:  https://app.taleez.com/api/public/applications
        numberOfDaysToConsider: ${TALEEZ_NB_DAYS:10}
        delayInMinutes: ${TALEEZ_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${TALEEZ_API_KEY:jenesuispasuncv_meubj7stjxixqf2v}
      - atsCode: taleez
        recruiterCode: ${TALEEZ_ACHIL_RECRUITER_CODE:S-21746}
        candidatureNotificationUrl: ${TALEEZ_SEND_CANDIDATURES_URL:https://demo.taleez.com/api/public/applications} # prod:  https://app.taleez.com/api/public/applications
        numberOfDaysToConsider: ${TALEEZ_NB_DAYS:10}
        delayInMinutes: ${TALEEZ_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${TALEEZ_API_KEY:jenesuispasuncv_meubj7stjxixqf2v}
      - atsCode: taleez
        recruiterCode: ${TALEEZ_GROUPELG_RECRUITER_CODE:S-21871}
        candidatureNotificationUrl: ${TALEEZ_SEND_CANDIDATURES_URL:https://demo.taleez.com/api/public/applications} # prod:  https://app.taleez.com/api/public/applications
        numberOfDaysToConsider: ${TALEEZ_NB_DAYS:10}
        delayInMinutes: ${TALEEZ_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${TALEEZ_API_KEY:jenesuispasuncv_meubj7stjxixqf2v}
  talent-plug:
    fetch:
      - atsCode: TALENT_PLUG
        recruiterCode: ${TALENT_PLUG_AREAS_RECRUITER_CODE:S-21508}
        remoteUrl: ${TALENT_PLUG_AREAS_FETCH_URL:https://www.mytalentplug.com/xml.aspx?jbID=86k1UuIvziM%3D&mid=wFB1SHaPgSM%3D}
        rootPath: /offers/offer
        configCode: AREAS
      - atsCode: TALENT_PLUG
        recruiterCode: ${TALENT_PLUG_BOFROST_RECRUITER_CODE:S-21680}
        remoteUrl: ${TALENT_PLUG_BOFROST_FETCH_URL:https://www.mytalentplug.com/xml.aspx?jbID=86k1UuIvziM%3D&mid=2X4CDY9gnYQ%3D}
        rootPath: /offers/offer
        configCode: BOFROST
      - atsCode: TALENT_PLUG
        remoteUrl: ${TALENT_PLUG_MARIETTON_REMOTE_URL:https://www.mytalentplug.com/xml.aspx?jbID=86k1UuIvziM%3D&mid=3mdQ8Jwcth8%3D}
        rootPath: /offers/offer
        recruiterProvider: PerOfferFetcher
        limiterServiceName: perOfferConfigCodeAndAuraInOutLimiter
        configCode: MARIETTON
    send:
      - atsCode: TALENT_PLUG
        recruiterCode: ${TALENT_PLUG_AREAS_RECRUITER_CODE:S-21508}
        candidatureNotificationMail: ${TALENT_PLUG_AREAS_FORCED_EMAIL:<EMAIL>}
        numberOfDaysToConsider: ${TALENT_PLUG_AREAS_NB_DAYS:10}
        delayInMinutes: ${TALENT_PLUG_AREAS_DELAY_IN_MINUTES:60}
      - atsCode: TALENT_PLUG
        recruiterCode: ${TALENT_PLUG_BOFROST_RECRUITER_CODE:S-21680}
        candidatureNotificationMail: ${TALENT_PLUG_BOFROST_FORCED_EMAIL:<EMAIL>}
        numberOfDaysToConsider: ${TALENT_PLUG_BOFROST_NB_DAYS:10}
        delayInMinutes: ${TALENT_PLUG_BOFROST_DELAY_IN_MINUTES:60}
      - atsCode: TALENT_PLUG
        recruitersProvider: PerDBConfigSender
        recruitersProviderConfigCode: MARIETTON
        candidatureNotificationMail: ${TALENT_PLUG_MARIETTON_FORCED_EMAIL:<EMAIL>}
        numberOfDaysToConsider: ${TALENT_PLUG_MARIETTON_NB_DAYS:10}
        delayInMinutes: ${TALENT_PLUG_MARIETTON_DELAY_IN_MINUTES:60}
  candidatus:
    fetch:
      - atsCode: CANDIDATUS
        recruiterCode: ${CANDIDATUS_ARTELOGE_RECRUITER_CODE:S-21382}
        remoteUrl: ${CANDIDATUS_ARTELOGE_REMOTE_URL:https://secure.candidatus.com/WebAPI?entid=158275&code=Hjeoplus9WCF/tc6dgAgg&method=jobs&sourceid=2929&location=yes&generic=yes}
        rootPath: /CandidatusWebAPIResponse/JobsList/Job
        configCode: ARTELOGE
    send:
      - atsCode: CANDIDATUS
        recruiterCode: ${CANDIDATUS_ARTELOGE_RECRUITER_CODE:S-21382}
        candidatureNotificationMail: ${CANDIDATUS_ARTELOGE_CANDIDATURE_MAIL:<EMAIL>}
        numberOfDaysToConsider: ${CANDIDATUS_NB_DAYS:10}
        delayInMinutes: ${CANDIDATUS_DELAY_IN_MINUTES:60}
        spontaneousCandidatureNotificationEmail: ${CANDIDATUS_ARTELOGE_SPONTANEOUS_CANDIDATURE_EMAIL:<EMAIL>}
  in-recruiting:
    fetch:
      - atsCode: IN_RECRUITING
        recruiterCode: ${IN_RECRUITING_AIRVANCE_RECRUITER_CODE:S-21646}
        # prod: https://inrecruitingfr.intervieweb.it/annunci.php?lang=fr&LAC=franceair&d=jenesuispasuncv.fr&k=c1ad13d5f2ee8d8b6d631e95eeecb778&format=json_en&utype=0
        remoteUrl: ${IN_RECRUITING_AIRVANCE_REMOTE_URL:https://inrecruitingfr.intervieweb.it/annunci.php?lang=fr&LAC=testapi&d=jenesuispasuncv.fr&k=c1ad13d5f2ee8d8b6d631e95eeecb778&format=json_en&utype=0}
        configCode: AIRVANCE-france-air
      - atsCode: IN_RECRUITING
        recruiterCode: ${IN_RECRUITING_VINCIFACILITIES_RECRUITER_CODE:S-21774}
        remoteUrl: ${IN_RECRUITING_VINCIFACILITIES_REMOTE_URL:https://inrecruitingfr.intervieweb.it/annunci.php?lang=fr&LAC=vincira&d=jenesuispasuncv.fr&k=c1ad13d5f2ee8d8b6d631e95eeecb778&format=json_en&utype=0}
        configCode: VINCI_FACILITIES
    send:
      - atsCode: IN_RECRUITING
        recruiterCode: ${IN_RECRUITING_AIRVANCE_RECRUITER_CODE:S-21646}
        candidatureNotificationUrl: ${IN_RECRUITING_AIRVANCE_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud} # prod:  https://inrecruitingfr.intervieweb.it
        numberOfDaysToConsider: ${IN_RECRUITING_AIRVANCE_NB_DAYS:10}
        delayInMinutes: ${IN_RECRUITING_AIRVANCE_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${IN_RECRUITING_AIRVANCE_API_KEY:API_TEST_26064;dc23f5ba3ac02121d7820a4581b42a}
      - atsCode: IN_RECRUITING
        recruiterCode: ${IN_RECRUITING_VINCIFACILITIES_RECRUITER_CODE:S-21774}
        candidatureNotificationUrl: ${IN_RECRUITING_VINCIFACILITIES_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud} # prod:  https://inrecruitingfr.intervieweb.it
        numberOfDaysToConsider: ${IN_RECRUITING_VINCIFACILITIES_NB_DAYS:10}
        delayInMinutes: ${IN_RECRUITING_VINCIFACILITIES _DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${IN_RECRUITING_VINCIFACILITIES_API_KEY:API_TEST_26064;dc23f5ba3ac02121d7820a4581b42a}
  gestmax:
    fetch:
      - atsCode: GESTMAX
        recruiterCode: ${GESTMAX_OPTEVEN_RECRUITER_CODE:S-21300}
        # prod avec les 2 query params page et pageItems : https://opteven.simply-jobs.fr/rest/v2/onlinevacancy?fields=vacancy_id,vacancy_ref,vacancy_title,vacancy_date,vacancy_activation_date,customer_description,vacancy_anonymous_customer,vacancy_anonymous_customer_name,publication_start_date,publication_end_date,is_unsolicited,vac_function,vac_orga_niveau_1,vac_orga_niveau_2,vac_contract,vac_town,vac_salaire
        remoteUrl: ${GESTMAX_OPTEVEN_REMOTE_URL:https://opteven.dev.gestmax.fr/rest/v2/onlinevacancy?fields=vacancy_id,vacancy_ref,vacancy_title,vacancy_date,vacancy_activation_date,customer_description,vacancy_anonymous_customer,vacancy_anonymous_customer_name,publication_start_date,publication_end_date,is_unsolicited,vac_function,vac_orga_niveau_1,vac_orga_niveau_2,vac_contract,vac_town,vac_salaire}
        configCode: OPTEVEN
        basicAuthentication: ${GESTMAX_OPTEVEN_AUTHENTICATION:JNSPUCV:KljFtC57m0JYx[eQWwX9GT54}
        pageIndexParam: page
        pageSizeParam: pageItems
        pageSize: 100
        totalCountParam: _pagination.total
      - atsCode: GESTMAX
        recruiterCode: ${GESTMAX_ELIORG_RECRUITER_CODE:S-0478}
        # prod avec les 2 query params page et pageItems : https://derichebourg-multiservices-contrats.dev.gestmax.fr/rest/v2/onlinevacancy?fields=vacancy_id,vacancy_ref,vacancy_title,vacancy_date,customer_company,cust_logo,vacancy_activation_date,customer_description,publication_start_date,publication_end_date,is_unsolicited,vac_contrat,vac_temps_travail,vac_duree,vac_pays,vac_dpt,vac_ville,vac_remuneration,department_name,dept_code_agence,vaclang_job_description,vaclang_job_details,vaclang_profile
        remoteUrl: ${GESTMAX_ELIORG_REMOTE_URL:https://derichebourg-multiservices-contrats.dev.gestmax.fr/rest/v2/onlinevacancy?fields=vacancy_id,vacancy_ref,vacancy_title,vacancy_date,customer_company,cust_logo,vacancy_activation_date,customer_description,publication_start_date,publication_end_date,is_unsolicited,vac_contrat,vac_temps_travail,vac_duree,vac_pays,vac_dpt,vac_ville,vac_remuneration,department_name,dept_code_agence,vaclang_job_description,vaclang_job_details,vaclang_profile}
        configCode: ELIOR_G
        basicAuthentication: ${GESTMAX_ELIORG_AUTHENTICATION:Jenesuispasuncv:HVVmjN1J*twYFXb2r2QIVM9i}
        pageIndexParam: page
        pageSizeParam: pageItems
        pageSize: 100
        totalCountParam: _pagination.total
    send:
      - atsCode: GESTMAX
        recruiterCode: ${GESTMAX_OPTEVEN_RECRUITER_CODE:S-21300}
        # dev: https://opteven.dev.gestmax.fr/rest/v2/application
        # prod: https://opteven.simply-jobs.fr/rest/v2/application
        candidatureNotificationUrl: ${GESTMAX_OPTEVEN_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud}
        numberOfDaysToConsider: ${GESTMAX_OPTEVEN_NB_DAYS:10}
        delayInMinutes: ${GESTMAX_OPTEVEN_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${GESTMAX_OPTEVEN_AUTHENTICATION:JNSPUCV:KljFtC57m0JYx[eQWwX9GT54}
        trackingId: 84
      - atsCode: GESTMAX
        recruiterCode: ${GESTMAX_ELIORG_RECRUITER_CODE:S-0478}
        # dev: https://derichebourg-multiservices-contrats.dev.gestmax.fr/rest/v2/application
        # prod: https://xxx/rest/v2/application
        candidatureNotificationUrl: ${GESTMAX_ELIORG_CANDIDATURES_URL:https://derichebourg-multiservices-contrats.dev.gestmax.fr/rest/v2/application}
        numberOfDaysToConsider: ${GESTMAX_ELIORG_NB_DAYS:10}
        delayInMinutes: ${GESTMAX_ELIORG_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${GESTMAX_ELIORG_AUTHENTICATION:Jenesuispasuncv:HVVmjN1J*twYFXb2r2QIVM9i}
        trackingId: ${GESTMAX_ELIORG_TRACKING_ID:43}
  talentsoft:
    fetch:
      - atsCode: TALENTSOFT
        recruiterCode: ${TALENTSOFT_GROUPAMA_RECRUITER_CODE:S-21687}
        # code orga=28 : "Groupama Rhône Alpes Auvergne" ; cf.  https://testgroupama-cand.talent-soft.com/api/v2/referential/origin
        # prod: https://offres.groupama-gan-recrute.com
        remoteUrl: ${TALENTSOFT_GROUPAMA_REMOTE_URL:https://testgroupama-cand.talent-soft.com/api/v2/offersummaries?apiCriteria.organisation=28}
        configCode: GROUPAMA
        rootPath: data
        pageIndexParam: start
        pageSizeParam: count
        pageSize: 50
        globalMaxNumberOfOffersAllowed: 1000
        totalCountParam: _pagination.total
        lazyInitServiceName: talentsoftLazyInitService
        lazyInitUrl: ${TALENTSOFT_GROUPAMA_DETAIL_URL:https://testgroupama-cand.talent-soft.com/api/v2/offers/getoffer}
        tokenAuthentication:
          url: ${TALENTSOFT_GROUPAMA_AUTH_URL:https://testgroupama-cand.talent-soft.com/api/token}
          client-id: ${TALENTSOFT_GROUPAMA_CLIENT_ID:9c2e2c41-5032-4a17-9eea-2b10bced9329}
          client-secret: ${TALENTSOFT_GROUPAMA_CLIENT_SECRET:JPRwz+nqRDdxXKahOOJrfRzdjtI=}
          grant-type: ${TALENTSOFT_GROUPAMA_GRANT_TYPE:client_credentials}
      - atsCode: TALENTSOFT
        recruiterCode: ${TALENTSOFT_MGEN_RECRUITER_CODE:S-21860}
        # prod: mgen-career.talent-soft.com
        remoteUrl: ${TALENTSOFT_MGEN_REMOTE_URL:https://testmgen-career.talent-soft.com/api/v2/offersummaries}
        configCode: MGEN
        rootPath: data
        pageIndexParam: start
        pageSizeParam: count
        pageSize: 50
        globalMaxNumberOfOffersAllowed: 1000
        totalCountParam: _pagination.total
        lazyInitServiceName: talentsoftLazyInitService
        lazyInitUrl: ${TALENTSOFT_MGEN_DETAIL_URL:https://testmgen-career.talent-soft.com/api/v2/offers/getoffer}
        tokenAuthentication:
          url: ${TALENTSOFT_MGEN_AUTH_URL:https://testmgen-career.talent-soft.com/api/token}
          client-id: ${TALENTSOFT_MGEN_CLIENT_ID:97e8ad88-4e07-4f69-aaca-add87e4c2232}
          client-secret: ${TALENTSOFT_MGEN_CLIENT_SECRET:cPcgnUG7+bIolPPm5yik3+C3LWA=}
          grant-type: ${TALENTSOFT_MGEN_GRANT_TYPE:client_credentials}
      - atsCode: TALENTSOFT
        recruiterCode: ${TALENTSOFT_ELIOR_RECRUITER_CODE:S-21864}
        # prod: https://carrieres.eliorgroup.com/
        remoteUrl: ${TALENTSOFT_ELIOR_REMOTE_URL:https://testelior-cand.talent-soft.com/api/v2/offersummaries}
        configCode: ELIOR
        rootPath: data
        pageIndexParam: start
        pageSizeParam: count
        pageSize: 50
        globalMaxNumberOfOffersAllowed: 1000
        totalCountParam: _pagination.total
        lazyInitServiceName: talentsoftLazyInitService
        lazyInitUrl: ${TALENTSOFT_ELIOR_DETAIL_URL:https://testelior-cand.talent-soft.com/api/v2/offers/getoffer}
        tokenAuthentication:
          url: ${TALENTSOFT_ELIOR_AUTH_URL:https://testelior-cand.talent-soft.com/api/token}
          client-id: ${TALENTSOFT_ELIOR_CLIENT_ID:8c401d22-9a71-4388-90ec-75c1933c5925}
          client-secret: ${TALENTSOFT_ELIOR_CLIENT_SECRET:rnXu9o76xearNdcwXkMfyiXYa4U=}
          grant-type: ${TALENTSOFT_ELIOR_GRANT_TYPE:client_credentials}
    send:
      - atsCode: TALENTSOFT
        recruiterCode: ${TALENTSOFT_GROUPAMA_RECRUITER_CODE:S-21687}
        # prod: https://offres.groupama-gan-recrute.com
        candidatureNotificationUrl: ${TALENTSOFT_GROUPAMA_CANDIDATURES_URL:https://testgroupama-cand.talent-soft.com/api/v2/applicants/applicationswithoutaccount}
        numberOfDaysToConsider: ${TALENTSOFT_GROUPAMA_NB_DAYS:10}
        delayInMinutes: ${TALENTSOFT_GROUPAMA_DELAY_IN_MINUTES:60}
        # PROD: 5622
        trackingId: ${TALENTSOFT_GROUPAMA_TRACKING_ID:5648}
        tokenAuthentication:
          url: ${TALENTSOFT_GROUPAMA_AUTH_URL:https://testgroupama-cand.talent-soft.com/api/token}
          client-id: ${TALENTSOFT_GROUPAMA_CLIENT_ID:9c2e2c41-5032-4a17-9eea-2b10bced9329}
          client-secret: ${TALENTSOFT_GROUPAMA_CLIENT_SECRET:JPRwz+nqRDdxXKahOOJrfRzdjtI=}
          grant-type: ${TALENTSOFT_GROUPAMA_GRANT_TYPE:client_credentials}
        custom:
          fileTypeId: 1025
      - atsCode: TALENTSOFT
        recruiterCode: ${TALENTSOFT_MGEN_RECRUITER_CODE:S-21860}
        # prod: https://mgen-career.talent-soft.com/api/token
        candidatureNotificationUrl: ${TALENTSOFT_MGEN_CANDIDATURES_URL:https://testmgen-career.talent-soft.com/api/v2/applicants/applicationswithoutaccount}
        numberOfDaysToConsider: ${TALENTSOFT_MGEN_NB_DAYS:10}
        delayInMinutes: ${TALENTSOFT_MGEN_DELAY_IN_MINUTES:60}
        trackingId: ${TALENTSOFT_MGEN_TRACKING_ID:13319}
        tokenAuthentication:
          url: ${TALENTSOFT_MGEN_AUTH_URL:https://testmgen-career.talent-soft.com/api/token}
          client-id: ${TALENTSOFT_MGEN_CLIENT_ID:97e8ad88-4e07-4f69-aaca-add87e4c2232}
          client-secret: ${TALENTSOFT_MGEN_CLIENT_SECRET:cPcgnUG7+bIolPPm5yik3+C3LWA=}
          grant-type: ${TALENTSOFT_MGEN_GRANT_TYPE:client_credentials}
        custom:
          fileTypeId: 1027
      - atsCode: TALENTSOFT
        recruiterCode: ${TALENTSOFT_ELIOR_RECRUITER_CODE:S-21864}
        # prod: https://carrieres.eliorgroup.com/
        candidatureNotificationUrl: ${TALENTSOFT_ELIOR_CANDIDATURES_URL:https://testelior-cand.talent-soft.com/api/v2/applicants/applicationswithoutaccount}
        numberOfDaysToConsider: ${TALENTSOFT_ELIOR_NB_DAYS:10}
        delayInMinutes: ${TALENTSOFT_ELIOR_DELAY_IN_MINUTES:60}
        trackingId: ${TALENTSOFT_ELIOR_TRACKING_ID:5370}
        tokenAuthentication:
          url: ${TALENTSOFT_ELIOR_AUTH_URL:https://testelior-cand.talent-soft.com/api/token}
          client-id: ${TALENTSOFT_ELIOR_CLIENT_ID:8c401d22-9a71-4388-90ec-75c1933c5925}
          client-secret: ${TALENTSOFT_ELIOR_CLIENT_SECRET:rnXu9o76xearNdcwXkMfyiXYa4U=}
          grant-type: ${TALENTSOFT_ELIOR_GRANT_TYPE:client_credentials}
        custom:
          fileTypeId: 935
  covea:
    enabled: ${COVEA_ENABLED:true}
    fetch:
      - atsCode: COVEA
        recruiterCode: ${COVEA_RECRUITER_CODE:S-21775}
        remoteUrl: ${COVEA_REMOTE_URL:}
        configCode: COVEA
        tokenAuthentication:
          client-id: ${COVEA_FTP_CLIENT_ID:}
          client-secret: ${COVEA_FTP_SECRET:}
    send:
      - atsCode: COVEA
        recruiterCode: ${COVEA_RECRUITER_CODE:S-21775}
        # Preprod:
        # candidatureNotificationUrl: ${COVEA_CANDIDATURES_URL:https://covea-pilot.csod.com/services/api/x/candidate/v1/application}
        candidatureNotificationUrl: ${COVEA_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud}
        numberOfDaysToConsider: ${COVEA_NB_DAYS:10}
        delayInMinutes: ${COVEA_DELAY_IN_MINUTES:60}
        tokenAuthentication:
          url: ${COVEA_API_AUTH_URL:https://covea-pilot.csod.com/services/api/oauth2/token}
          client-id: ${COVEA_API_CLIENT_ID:1limm71b3iwp5}
          client-secret: ${COVEA_API_CLIENT_SECRET:2f79c3b8a152ea46a94c15456c32a79c10296d8caef481123e6a3e0a9ef40113}
          grant-type: ${TALENTSOFT_GROUPAMA_GRANT_TYPE:client_credentials}
          json: true
          scope: candidateandjobapplication:create
  teamtailor:
    fetch:
      - atsCode: TEAMTAILOR
        configCode: ALL
        remoteUrl: ${TEAMTAILOR_REMOTE_URL:https://teamtailor-production.s3.eu-west-1.amazonaws.com/feeds/jenesuispasuncv_-_le_jobboard-eab79c85-bd4d-4d8b-8c9a-5f5243c1ce84.xml}
        rootPath: /source/job
        recruiterProvider: PerOfferFetcherCreateUnknownRecruiter
        limiterServiceName: perRecruiterLimiter
    send:
      - atsCode: TEAMTAILOR
        # candidatureNotificationUrl: ${TEAMTAILOR_CANDIDATURES_URL:https://5qbn6o9x4h.execute-api.eu-west-1.amazonaws.com/production/apply}
        candidatureNotificationUrl: ${TEAMTAILOR_CANDIDATURES_URL:https://erhgo.wiremockapi.cloud}
        numberOfDaysToConsider: ${TEAMTAILOR_NB_DAYS:10}
        delayInMinutes: ${TEAMTAILOR_DELAY_IN_MINUTES:60}
        candidatureNotificationApiKey: ${TEAMTAILOR_AUTHENTICATION:4a9854f7-4ca8-40f4-8d2c-dc4aa8c119b9}
        recruitersProvider: PerDBConfigSender
        recruitersProviderConfigCode: ALL
  firecrawl:
    enabled: ${STEF_ENABLED:true}
    fetch:
      - atsCode: FIRECRAWL
        recruiterCode: ${STEF_RECRUITER_CODE:S-21912}
        remoteUrl: ${STEF_REMOTE_URL:https://stef.jobs/fr/postuler/}
        configCode: STEF
        requiresConfirmation: ${STEF_REQUIRES_CONFIRMATION:false}
        offerUrlMustContain: job-invite
        lazyInitServiceName: firecrawlLazyInitService
        limiterServiceName: globalLimiter
        isSinglePassPerDay: true
      - atsCode: FIRECRAWL
        recruiterCode: ${EST_METROPOLE_RECRUITER_CODE:S-21913}
        remoteUrl: ${EST_METROPOLE_REMOTE_URL:https://www.est-metropole-habitat.fr/recrutement/nos-offres-demploi/?pagination={page}}
        configCode: EST_METROPOLE
        requiresConfirmation: ${EST_METROPOLE_REQUIRES_CONFIRMATION:false}
        offerUrlMustContain: ${EST_METROPOLE_URL_FILTER:/offres-emploi/}
        offerUrlMustNotContain: ${EST_METROPOLE_URL_EXCLUDE:candidature-spontanee}
        maxPagesToScrape: ${EST_METROPOLE_MAX_PAGES:2}
        lazyInitServiceName: firecrawlLazyInitService
        limiterServiceName: globalLimiter
        isSinglePassPerDay: true
      - atsCode: FIRECRAWL
        recruiterCode: ${APICIL_RECRUITER_CODE:S-21914}
        remoteUrl: ${APICIL_REMOTE_URL:https://www.apicil-recrute.com/offres-demplois/page/{page}/}
        configCode: APICIL
        requiresConfirmation: ${APICIL_REQUIRES_CONFIRMATION:false}
        offerUrlMustContain: ${APICIL_URL_FILTER:/offres/}
        maxPagesToScrape: ${APICIL_MAX_PAGES:3}
        lazyInitServiceName: firecrawlLazyInitService
        limiterServiceName: globalLimiter
        isSinglePassPerDay: true
    send:
      - atsCode: FIRECRAWL
        recruiterCode: ${STEF_RECRUITER_CODE:S-21912}
        candidatureNotificationMail: ${STEF_CANDIDATURE_MAIL:<EMAIL>}
        numberOfDaysToConsider: ${STEF_NB_DAYS:10}
        delayInMinutes: ${STEF_DELAY_IN_MINUTES:60}
      - atsCode: FIRECRAWL
        recruiterCode: ${EST_METROPOLE_RECRUITER_CODE:S-21913}
        candidatureNotificationMail: ${EST_METROPOLE_CANDIDATURE_MAIL:<EMAIL>}
        numberOfDaysToConsider: ${EST_METROPOLE_NB_DAYS:10}
        delayInMinutes: ${EST_METROPOLE_DELAY_IN_MINUTES:60}
      - atsCode: FIRECRAWL
        recruiterCode: ${APICIL_RECRUITER_CODE:S-21914}
        candidatureNotificationMail: ${APICIL_CANDIDATURE_MAIL:<EMAIL>}
        numberOfDaysToConsider: ${APICIL_NB_DAYS:10}
        delayInMinutes: ${APICIL_DELAY_IN_MINUTES:60}

geo:
  url: https://api-adresse.data.gouv.fr/search?q={q}&type={type}

---
spring:
  config:
    activate:
      on-profile: '!test'
  jpa:
    properties:
      jakarta:
        persistence:
          sharedCache:
            mode: ENABLE_SELECTIVE
      hibernate:
        # generate_statistics: true
        default_batch_fetch_size: 100
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: com.hazelcast.hibernate.HazelcastLocalCacheRegionFactory
logging:
  config: classpath:logback-spring.xml
  level:
    ROOT: INFO
    com:
      erhgo: DEBUG
      #hazelcast: TRACE
      openhtmltopdf: ERROR
    org:
      springframework:
        data:
          jpa:
            repository:
              query: WARN
      zalando:
        logbook: TRACE
      keycloak:
        adapters:
          BearerTokenRequestAuthenticator: OFF
      hibernate:
      # SQL: DEBUG
      # orm.jdbc.bind: TRACE
      apache:
        pdfbox:
          pdmodel: ERROR
    net:
      javacrumbs:
        shedlock: DEBUG
---

spring:
  config:
    activate:
      on-profile: default
  devtools:
    restart:
      enabled: true
logging:
  config: classpath:logback-spring-local.xml
  level:
    com:
      openhtmltopdf:
        cssparse: ERROR
    org:
      apache:
        pdfbox:
          pdmodel:
            font:
              PDTrueTypeFont: ERROR
              PDType1Font: ERROR
keycloak-realms:
  front_office_redirects:
    - "*"
  back_office_redirects:
    - "*"
  sourcing_redirects:
    - "*"
  front_office_base_url: http://localhost:53323
  back_office_base_url: http://localhost:8070
  sourcing_base_url: http://localhost:9000

---

spring:
  config:
    activate:
      on-profile: e2e
keycloak-realms:
  front_office_redirects:
    - "*"
  back_office_redirects:
    - "*"
  sourcing_redirects:
    - "*"
  front_office_base_url: http://front-office-e2e.localhost
  back_office_base_url: http://back-office-e2e.localhost
  sourcing_base_url: http://sourcing-e2e.localhost

---

spring:
  config:
    activate:
      on-profile: testing
keycloak-realms:
  front_office_redirects:
    - https://testing.odas.app
    - https://testing.erhgo.fr
    - https://testing.jenesuispasuncv.fr
  back_office_redirects:
    - https://testing-bo.odas.app
    - https://testing-bo.erhgo.fr
    - https://testing-bo.jenesuispasuncv.fr
  sourcing_redirects:
    - https://testing-jerecrute.jenesuispasuncv.fr
  front_office_base_url: https://testing.jenesuispasuncv.fr
  back_office_base_url: https://testing-bo.jenesuispasuncv.fr
  sourcing_base_url: https://testing-jerecrute.jenesuispasuncv.fr
trimoji:
  callbackUrl: ${TRIMOJI_CALLBACK_URL:https://testing-api.jenesuispasuncv.fr/api/odas/public/trimoji}
---

spring:
  config:
    activate:
      on-profile: staging
keycloak-realms:
  front_office_redirects:
    - https://staging.odas.app
    - https://staging.erhgo.fr
    - https://staging.jenesuispasuncv.fr
  back_office_redirects:
    - https://staging-bo.odas.app
    - https://staging-bo.erhgo.fr
    - https://staging-bo.jenesuispasuncv.fr
  sourcing_redirects:
    - https://staging-jerecrute.jenesuispasuncv.fr
  front_office_base_url: https://staging.jenesuispasuncv.fr
  back_office_base_url: https://staging-bo.jenesuispasuncv.fr
  sourcing_base_url: https://staging-jerecrute.jenesuispasuncv.fr
sendinblue:
  forcedSourcingRecipients: ${FORCED_SOURCING_RECIPIENTS:<EMAIL>}
trimoji:
  callbackUrl: ${TRIMOJI_CALLBACK_URL:https://staging-api.jenesuispasuncv.fr/api/odas/public/trimoji}
slack:
  url-for-message-type:
    EmailSendingNotifierMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C041BRNLV4N}
    ExperienceOnUnqualifiedOccupationMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C041BRNLV4N}
    UnqualifiedCreatedOccupationMessageDTO: ${SLACK_UNQUALIFIED_CREATED_OCCUPATION_NOTIFICATION_CHANNEL_ID:C07365G7S8J}
    AddedAlternativeLabelMessageDTO: ${SLACK_UNQUALIFIED_CREATED_OCCUPATION_NOTIFICATION_CHANNEL_ID:C07365G7S8J}
    SourcingJobOnOccupationMessage: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C041BRNLV4N}
    SourcingPublishRecruitmentMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C041BRNLV4N}
    RemoteOfferMessageDTO: ${SLACK_REMOTE_OFFER_CHANNEL_ID:C07QB0JJPTN}
    CSVImportEndsMessageDTO: ${SLACK_CSV_CHANNEL_ID:C08C5LLG59S}
    CSVImportStartsMessageDTO: ${SLACK_CSV_CHANNEL_ID:C08C5LLG59S}
    ATSRecruiterCreated: ${SLACK_ATS_RECRUITER_CHANNEL_ID:C08C5LLG59S}
---

spring:
  config:
    activate:
      on-profile: master
keycloak-realms:
  front_office_redirects:
    - https://odas.app
    - https://app.erhgo.fr
    - https://app.jenesuispasuncv.fr
  back_office_redirects:
    - https://bo.odas.app
    - https://bo.erhgo.fr
    - https://bo.jenesuispasuncv.fr
  sourcing_redirects:
    - https://jerecrute.jenesuispasuncv.fr
  front_office_base_url: https://app.jenesuispasuncv.fr
  back_office_base_url: https://bo.jenesuispasuncv.fr
  sourcing_base_url: https://jerecrute.jenesuispasuncv.fr
slack:
  url-for-message-type:
    DataHealthCheckMessage: ${SLACK_DATA_HEALTH_CHECK_NOTIFICATION_CHANNEL_ID:C04SZLXUBEF}
    EmailSendingNotifierMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C041FERRBRB}
    ExperienceOnUnqualifiedOccupationMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C041FERRBRB}
    UnqualifiedCreatedOccupationMessageDTO: ${SLACK_UNQUALIFIED_CREATED_OCCUPATION_NOTIFICATION_CHANNEL_ID:C0738KP3U5S}
    AddedAlternativeLabelMessageDTO: ${SLACK_UNQUALIFIED_CREATED_OCCUPATION_NOTIFICATION_CHANNEL_ID:C0738KP3U5S}
    FrontofficeAccountDeletionNotifierMessageDTO: ${SLACK_USER_DELETION_NOTIFICATION_CHANNEL_ID:C050PJ0E19U}
    FrontofficeNotifierMessageDTO: ${SLACK_USER_NOTIFICATION_CHANNEL_ID:C01EL2LTKT6}
    FrontOfficePersonalEmailDomainMessageDTO: ${SLACK_USER_PERSONAL_EMAIL_DOMAIN_LIST_CHANNEL_ID:C050PJ0E19U}
    SourcingDisabledAccountMessageDTO: ${SLACK_SOURCING_USER_NOTIFICATION_CHANNEL_ID:C041L12UNQ3}
    SourcingInvitationUsedMessageDTO: ${SLACK_SOURCING_USER_NOTIFICATION_CHANNEL_ID:C041L12UNQ3}
    SourcingJobOnOccupationMessage: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C041FERRBRB}
    SourcingNewUserMessageDTO: ${SLACK_SOURCING_USER_NOTIFICATION_CHANNEL_ID:C041L12UNQ3}
    SourcingPublishRecruitmentMessageDTO: ${SLACK_OCCUPATION_NOTIFICATION_CHANNEL_ID:C041FERRBRB}
    RemoteOfferMessageDTO: ${SLACK_REMOTE_OFFER_CHANNEL_ID:C07QB0NFXJ8}
    RemoteOfferErrorDTO: ${SLACK_REMOTE_OFFER_CHANNEL_ID:C041FERRBRB}
    CSVImportEndsMessageDTO: ${SLACK_CSV_CHANNEL_ID:C08C32QJEF5}
    CSVImportStartsMessageDTO: ${SLACK_CSV_CHANNEL_ID:C08C32QJEF5}
    ATSRecruiterCreated: ${SLACK_ATS_RECRUITER_CHANNEL_ID:C074USGGZE3}

sendinblue:
  forcedSourcingRecipients: ${FORCED_SOURCING_RECIPIENTS:<EMAIL>}
trimoji:
  callbackUrl: ${TRIMOJI_CALLBACK_URL:https://api.jenesuispasuncv.fr/api/odas/public/trimoji}
gmail:
  senderEmail: <EMAIL>
