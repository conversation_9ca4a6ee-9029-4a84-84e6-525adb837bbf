package com.erhgo.services.externaloffer.firecrawl;

import com.erhgo.services.http.RetryableHttpClient;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.List;
import java.util.stream.StreamSupport;


@Slf4j
class FirecrawlScraperPromptAndClientTest {

    private static final String API_KEY = "fc-xxxx";
    private static final String API_URL = "https://api.firecrawl.dev/v1/scrape";
    private static final String EXTRACTION_PROMPT = """
             Extraire uniquement la section de description complète du poste en HTML.
            Conserver toutes les balises HTML d'origine (div, span, ul, li, etc.) et les attributs de style.
            Important:
            - Inclure uniquement le contenu qui décrit le poste, les responsabilités, les qualifications requises,
              les avantages, et autres informations relatives au poste.
            - Ne pas inclure les formulaires de candidature, les boutons pour postuler, les menus de navigation,
              les en-têtes du site, les pieds de page, ou tout autre élément qui n'est pas directement lié à la
              description du poste.
            - Exclure les tableaux de navigation (comme les flèches directionnelles ou zoom).
            - Ignorer les sections de politique de confidentialité, cookies, ou d'autres éléments non pertinents.
            
            Sérialise l'offre au format JSON avec les propriétés suivantes:
            - title: Titre du poste
            - contract: Type de contrat (CDI ou CDD, ou autres types)
            - location: Lieu de travail
            - salary: Salaire annuel si disponible (convertir en format "XXXXX€" si nécessaire), sinon null
            - url: URL de l'offre %s
            - job_description: Description complète du poste en HTML
            - organization_description: Description de l'entreprise ou organisation offrant le poste (extraire toute section "À propos de nous", "Qui sommes-nous", ou similaire qui présente l'entreprise)
            - publication_date: UNIQUEMENT la date de publication de l'annonce (quand l'offre a été publiée sur le site) au format dd-MM-yyyy'T'HH:mm, sinon null. 
            
            INTERDICTION ABSOLUE: Ne JAMAIS utiliser les dates suivantes pour publication_date:
            * "Poste à pourvoir à partir du" ou "à pourvoir dès le" + date
            * "Prise de poste le" + date
            * "Date de début souhaitée" + date
            * "Début de mission" + date
            * "Entrée en fonction" + date
            * "Date de démarrage" + date
            * "Disponibilité requise" + date
            Ces dates concernent le DÉBUT DU TRAVAIL, pas la publication de l'annonce.
            
            Si aucune date de publication explicite n'est trouvée (comme "Publié le", "Annonce du", "Mise en ligne le"), alors publication_date doit être null.
            Si le salaire est indiqué sous forme "€30k" ou "30k€", convertir en "30000€".
            
               Pour l'organization_description, chercher spécifiquement:
            - Les sections décrivant l'entreprise, sa mission, ses valeurs
            - Les sections intitulées "À propos de [nom de l'entreprise]", "L'entreprise", "Qui sommes-nous", etc.
            - Les paragraphes décrivant l'historique, la taille, le secteur d'activité de l'entreprise
            - Les sections sur la culture d'entreprise, l'environnement de travail
            - Toutes les mentions de l'entreprise dans le contexte de l'offre d'emploi
            
            Ne jamais renvoyer de valeur vide pour organization_description. Si aucune information sur l'entreprise n'est
            trouvée explicitement, analyser le contenu général de la page pour extraire toute mention contextuelle de l'entreprise. N'invente pas de contenu.
            """;
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @Disabled()
    void scrapeJobUrls() {
        var careerPageUrl = "https://www.est-metropole-habitat.fr/recrutement/nos-offres-demploi/?";

        log.info("===== TEST: Extraction des URLs d'offres d'emploi =====");
        log.info("URL de la page carrière: {}", careerPageUrl);

        var jobUrls = scrapeJobUrls(careerPageUrl);

        log.info("\nRésultats - {} offres trouvées:", jobUrls.size());
        for (int i = 0; i < jobUrls.size(); i++) {
            log.info("{}. {}", (i + 1), jobUrls.get(i));
        }
    }


    @Test
    @Disabled()
    void scrapeJobDetails() {
        var jobUrl = "https://www.apicil-recrute.com/offres/4-conseillers-retraite-f-h/";

        log.info("===== TEST: Extraction des détails d'une offre d'emploi =====");
        log.info("URL de l'offre: {}", jobUrl);
        long startTime = System.currentTimeMillis();

        var details = scrapeJobDetails(jobUrl);

        long executionTime = System.currentTimeMillis() - startTime;
        log.info("\nTemps d'exécution: {} ms", executionTime);

        log.info("\nRésultats:");
        log.info("Titre: {}", details.path("title").asText());
        log.info("Type de contrat: {}", details.path("contract").asText());
        log.info("Lieu: {}", details.path("location").asText());
        log.info("Salaire: {}", details.path("salary").asText());
        log.info("Date de publication: {}", details.path("publication_date").asText());
        log.info("\nDescription du poste:");
        log.info("{}", details.path("job_description").asText());
        log.info("\nDescription de l'organisation:");
        log.info("{}", details.path("organization_description").asText());
    }

    @Test
    @Disabled()
    void fullScraping() {
        var careerPageUrl = "https://stef.jobs/fr/postuler/";
        int maxJobs = 2;

        log.info("===== TEST: Scraping complet (URLs + détails) =====");
        log.info("URL de la page carrière: {}", careerPageUrl);

        var jobUrls = scrapeJobUrlsWithRetryableClient(careerPageUrl);
        log.info("\nTotal d'offres trouvées: {}", jobUrls.size());

        var limitedJobUrls = jobUrls.stream().limit(maxJobs).toList();
        log.info("Traitement de {} offres...\n", limitedJobUrls.size());

        for (int i = 0; i < limitedJobUrls.size(); i++) {
            var url = limitedJobUrls.get(i);
            log.info("--- Offre {} ---", (i + 1));
            log.info("URL: {}", url);

            var details = scrapeJobDetailsWithRetryableClient(url);

            log.info("Titre: {}", details.path("title").asText());
            log.info("Type de contrat: {}", details.path("contract").asText());
            log.info("Lieu: {}", details.path("location").asText());
            log.info("Salaire: {}", details.path("salary").asText());
            log.info("");
        }
    }


    @SneakyThrows
    private List<String> scrapeJobUrls(String url) {
        var requestBody = objectMapper.createObjectNode();
        requestBody.put("url", url);
        requestBody.putArray("formats").add("links");

        var headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(API_KEY);

        var response = restTemplate.exchange(
                API_URL,
                HttpMethod.POST,
                new HttpEntity<>(objectMapper.writeValueAsString(requestBody), headers),
                String.class
        );

        var responseBody = objectMapper.readTree(response.getBody());
        var linksNode = responseBody.path("data").path("links");

        return StreamSupport.stream(linksNode.spliterator(), false)
                .map(JsonNode::asText)
                .toList();
    }

    @SneakyThrows
    private JsonNode scrapeJobDetails(String jobUrl) {
        var requestBody = objectMapper.createObjectNode();
        requestBody.put("url", jobUrl);
        requestBody.putArray("formats").add("extract").add("html");

        var extractNode = requestBody.putObject("extract");
        extractNode.put("prompt", EXTRACTION_PROMPT);
        requestBody.put("timeout", 80000);
        requestBody.put("waitFor", 10000);
        var headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(API_KEY);

        var response = restTemplate.exchange(
                API_URL,
                HttpMethod.POST,
                new HttpEntity<>(objectMapper.writeValueAsString(requestBody), headers),
                String.class
        );

        var responseJson = objectMapper.readTree(response.getBody());
        return responseJson.path("data").path("extract");
    }


    @SneakyThrows
    private JsonNode scrapeJobDetailsWithRetryableClient(String jobUrl) {
        var httpClient = new RetryableHttpClient(60, 60, 60);

        var requestBodyJson = objectMapper.createObjectNode();
        requestBodyJson.put("url", jobUrl);
        requestBodyJson.putArray("formats").add("extract").add("html");

        var extractNode = requestBodyJson.putObject("extract");
        extractNode.put("prompt", EXTRACTION_PROMPT);
        requestBodyJson.put("waitFor", 10000);

        var requestBody = okhttp3.RequestBody.create(
                objectMapper.writeValueAsString(requestBodyJson),
                okhttp3.MediaType.parse("application/json")
        );

        var request = new okhttp3.Request.Builder()
                .url(API_URL)
                .post(requestBody)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + API_KEY)
                .build();

        try (var response = httpClient.executeRequestWithStatusCheck(request)) {
            if (response.body() == null) {
                throw new RuntimeException("Empty response body from Firecrawl API");
            }

            String responseBodyString = response.body().string();
            var responseJson = objectMapper.readTree(responseBodyString);
            return responseJson.path("data").path("extract");
        } catch (IOException e) {
            log.error("Error scraping job details for URL: {}", jobUrl, e);
            throw new RuntimeException("Failed to scrape job details", e);
        }
    }

    @SneakyThrows
    private List<String> scrapeJobUrlsWithRetryableClient(String url) {
        var httpClient = new RetryableHttpClient(60, 60, 60);

        var requestBodyJson = objectMapper.createObjectNode();
        requestBodyJson.put("url", url);
        requestBodyJson.putArray("formats").add("links");

        var requestBody = okhttp3.RequestBody.create(
                objectMapper.writeValueAsString(requestBodyJson),
                okhttp3.MediaType.parse("application/json")
        );

        var request = new okhttp3.Request.Builder()
                .url(API_URL)
                .post(requestBody)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + API_KEY)
                .build();

        try (var response = httpClient.executeRequestWithStatusCheck(request)) {
            if (response.body() == null) {
                throw new RuntimeException("Empty response body from Firecrawl API");
            }

            String responseBodyString = response.body().string();
            var responseJson = objectMapper.readTree(responseBodyString);
            var linksNode = responseJson.path("data").path("links");

            return StreamSupport.stream(linksNode.spliterator(), false)
                    .map(JsonNode::asText)
                    .filter(link -> link.contains("job-invite"))
                    .toList();
        } catch (IOException e) {
            log.error("Error scraping job URLs for career page: {}", url, e);
            throw new RuntimeException("Failed to scrape job URLs", e);
        }
    }
}
