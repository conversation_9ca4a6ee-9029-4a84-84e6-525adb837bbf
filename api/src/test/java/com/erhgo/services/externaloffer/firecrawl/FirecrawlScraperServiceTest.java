package com.erhgo.services.externaloffer.firecrawl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FirecrawlScraperServiceTest {

    private static final String JOB_INVITE_FILTER = "job-invite";
    private static final String JOB_URL_MUST_NOT_CONTAIN = "candidature-spontanee";
    private final ObjectMapper realObjectMapper = createObjectMapper();
    @InjectMocks
    private FirecrawlScraperService scraperService;
    @Mock
    private RestTemplate restTemplate;

    private static ObjectMapper createObjectMapper() {

        var formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy'T'HH:mm");
        return new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .registerModule(new JavaTimeModule())
                .registerModule(new SimpleModule().addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter)).addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter)));
    }

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(scraperService, "restTemplate", restTemplate);
        ReflectionTestUtils.setField(scraperService, "objectMapper", realObjectMapper);
        ReflectionTestUtils.setField(scraperService, "apiUrl", "https://api.example.com");
        ReflectionTestUtils.setField(scraperService, "apiKey", "api-key");

    }

    @Test
    void scrapeJobsAtUrl_returnJobUrls() {
        var url = "https://careers.example.com";
        var responseJson = """
                {
                  "data": {
                    "links": [
                      "https://careers.example.com/job-invite/1",
                      "https://careers.example.com/job-invite/2",
                      "https://careers.example.com/some-other-page",
                       "https://careers.example.com/job-invite/candidature-spontanee"
                    ]
                  }
                }
                """;
        var responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenReturn(responseEntity);
        var result = scraperService.scrapeJobsAtUrl(url, JOB_INVITE_FILTER, JOB_URL_MUST_NOT_CONTAIN);

        assertThat(result)
                .hasSize(2)
                .contains(
                        "https://careers.example.com/job-invite/1",
                        "https://careers.example.com/job-invite/2"
                );
    }

    @Test
    void scrapeJobDetails_returnsExtractedData() {
        var jobUrl = "https://careers.example.com/job-invite/1";

        var extractNode = realObjectMapper.createObjectNode();
        extractNode.put("title", "Software Engineer");
        extractNode.put("contract", "CDI");
        extractNode.put("location", "Paris");

        var dataNode = realObjectMapper.createObjectNode();
        dataNode.set("extract", extractNode);

        var responseNode = realObjectMapper.createObjectNode();
        responseNode.set("data", dataNode);
        var responseJson = responseNode.toString();
        var responseEntity = new ResponseEntity<>(responseJson, HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenReturn(responseEntity);

        var result = scraperService.scrapeJobDetails(jobUrl);

        assertThat(result.path("title").asText()).isEqualTo("Software Engineer");
        assertThat(result.path("contract").asText()).isEqualTo("CDI");
        assertThat(result.path("location").asText()).isEqualTo("Paris");
    }


    @Test
    @SneakyThrows
    void scrapeJobsAtUrl_handleApiError() {
        var careerPageUrl = "https://careers.example.com";
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenThrow(new RestClientResponseException("Bad Request", 400, "Bad Request", null, null, null));

        var jobUrls = scraperService.scrapeJobsAtUrl(careerPageUrl, JOB_INVITE_FILTER, JOB_URL_MUST_NOT_CONTAIN);

        assertThat(jobUrls).isEmpty();
    }


    @Test
    @SneakyThrows
    void scrapeJobDetails_handleApiError() {
        var jobUrl = "https://careers.example.com/job-invite/1";
        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenThrow(new RestClientResponseException("Server Error", 500, "Server Error", null, null, null));

        var jobDetails = scraperService.scrapeJobDetails(jobUrl);

        assertThat(jobDetails.isNull()).isTrue();
        assertThat(jobDetails.isEmpty()).isTrue();
    }


    @Test
    @SneakyThrows
    void scrapeJobDetails_shouldParseIntoScrappedJob() {
        var jobUrl = "https://careers.example.com/job-invite/1";
        var responseJson = """
                {
                  "data": {
                    "extract": {
                      "title": "Senior Software Engineer",
                      "contract": "CDI",
                      "location": "Paris",
                      "salary": "60000€",
                      "url": "https://careers.example.com/job-invite/1",
                      "job_description": "<div>Job description content</div>",
                      "organization_description": "<div>Organization description</div>",
                      "publication_date": "24-03-2025T00:00"
                    }
                  }
                }
                """;

        when(restTemplate.exchange(
                anyString(),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenReturn(new ResponseEntity<>(responseJson, HttpStatus.OK));


        var jobDetails = scraperService.scrapeJobDetails(jobUrl);
        var job = realObjectMapper.treeToValue(jobDetails, ScrappedJob.class);

        assertThat(job).isNotNull();
        assertThat(job.getOfferTitle()).isEqualTo("Senior Software Engineer");
        assertThat(job.getContractType()).isEqualTo("CDI");
        assertThat(job.getLocation()).isEqualTo("Paris");
        assertThat(job.getJobDescription()).isEqualTo("<div>Job description content</div>");
        assertThat(job.getOrganizationDescription()).isEqualTo("<div>Organization description</div>");
    }
}
